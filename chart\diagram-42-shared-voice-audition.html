<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享业务流程: 音色试听流程（可复用标准流程）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .features {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding-left: 0;
        }
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .reference-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .reference-box h4 {
            color: #856404;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='all-diagrams-index.html'">← 返回图表索引</button>

    <div class="container">
        <h1>🎵 共享业务流程: 音色试听流程（可复用标准流程）</h1>

        <div class="description">
            <strong>流程说明：</strong>这是一个独立的、可复用的音色试听业务流程，专门设计为供分镜旁白配置与试听预览功能引用的标准化组件。任何需要音色试听功能的业务流程（如分镜编辑、项目管理、音频处理等）都可以通过参数化调用这个统一的音色试听流程。所有UI交互统一由"Py视频创作工具前端"处理，确保用户体验的一致性。支持获取项目角色列表、设置分镜解说角色、获取火山音色列表、设置分镜旁白音色、试听对白生成等完整功能。
        </div>

        <div class="reference-box">
            <h4>🔗 引用的共享流程</h4>
            <p><strong>Token验证流程：</strong>本流程中的所有认证步骤都引用 <code>diagram-22-python-token-validation.html</code> 标准流程，确保认证逻辑的统一性和安全性。</p>
        </div>

        <div class="mermaid" id="diagram">
sequenceDiagram
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    Note over F: 🔄 用户发起音色试听功能

    F->>F: 启动音色试听流程<br/>参数: {storyboard_id, mode}

    Note over F: 📋 参数化配置处理
    alt mode=preview（快速试听）
        Note over F: 使用当前配置，快速生成试听
    else mode=configure（完整配置）
        Note over F: 提供完整的音色配置功能
    end

    Note over F: 🎨 统一UI处理
    F->>F: 弹出音色试听配置界面
    F->>A: 获取音色试听初始数据

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限

    alt Token验证失败
        A->>F: 返回认证失败<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
        F->>F: 显示登录提示
    else Token验证通过
        A->>DB: 查询用户状态和权限
        F->>A: GET /py-api/projects/characters
        A->>DB: 查询项目绑定角色列表
        DB->>A: 返回角色数据
        A->>F: 返回角色列表配置数据
        F->>F: 渲染音色试听界面

        Note over F: 🎭 音色试听模式选择
        F->>F: 根据参数显示对应的配置选项

        alt 用户选择解说角色
            F->>F: 显示角色选择下拉界面
            Note over F: 用户选择解说角色，绑定到分镜
        else 用户选择音色配置
            F->>F: 显示音色参数配置界面
            Note over F: 用户配置语言、情绪、语速、音调
        end

        F->>F: 用户选择音色(根据mode参数决定可选范围)

        Note over F: 🤖 智能音色选择流程
        F->>A: POST /py-api/voices/providers/volcengine/list<br/>type=traditional, auto_recommend=true
        A->>A: 调用VoiceController音色选择服务
        A->>A: 调用智能平台选择服务
        A->>SC: 调用AiServiceClient
        SC->>DB: 查询用户历史偏好和使用记录
        SC->>SC: 分析用户偏好+音色特性+分镜内容
        alt 推荐失败/不可用
            A->>F: 提示手动选择或使用默认音色
        else 推荐成功
            SC->>A: 返回最佳推荐+备选方案
            A->>F: 返回音色推荐结果
            F->>F: 显示音色选择界面
        end

        F->>F: 用户确认配置参数，点击试听按钮

        Note over F: 🔗 建立WebSocket连接
        F->>W: POST /py-api/websocket/auth<br/>认证WebSocket连接<br/>{<br/>  "client_type": "python_tool",<br/>  "user_token": "Bearer xxx",<br/>  "business_type": "voice_audition"<br/>}
        W->>W: 验证用户Token和客户端类型
        W->>DB: 创建WebSocket会话记录
        W->>F: 返回WebSocket连接信息<br/>{<br/>  code: 200,<br/>  message: "WebSocket认证成功",<br/>  data: {<br/>    session_id: "ws_session_xxx",<br/>    websocket_url: "wss://api.tiptop.cn:8080",<br/>    expires_at: "2025-01-09T10:30:00Z",<br/>    supported_events: [<br/>      "ai_generation_progress",<br/>      "ai_generation_completed",<br/>      "ai_generation_failed",<br/>      "voice_synthesis_completed",<br/>      "points_changed"<br/>    ],<br/>    heartbeat_interval: 30<br/>  }<br/>}
        F->>W: 连接到WebSocket服务器<br/>wss://api.tiptop.cn:8080?session_id=ws_session_xxx
        W->>F: WebSocket连接确认，准备接收进度推送

        F->>A: POST /py-api/voice/preview-with-websocket<br/>音色试听请求<br/>{<br/>  "storyboard_id": 123,<br/>  "voice_id": "volcengine_voice_001",<br/>  "text": "这是试听文本",<br/>  "websocket_session_id": "ws_session_xxx"<br/>}
        A->>A: 创建异步任务ProcessVoicePreview
        A->>F: 立即返回任务信息<br/>{<br/>  "task_id": "voice_preview_123456",<br/>  "status": "processing"<br/>}

        Note over A: 🔄 异步任务开始处理
        A->>R: 幂等校验（同参数短期复用 last_preview_url）
        alt 幂等命中
            A->>W: 返回历史试听URL（命中缓存）
            W->>F: 推送命中缓存结果
        else 非命中
            A->>A: 参数校验（storyboard_id、解说角色/音色/旁白文本）
            alt 参数无效
                A->>W: 返回参数错误 422
                W->>F: 推送参数错误
                F->>F: 提示请先配置音色/角色/旁白文本
            else 参数有效
                A->>A: 校验前置条件（已配置解说角色+音色+有效文本）
                alt 前置条件缺失
                    A->>W: 返回 422 “请先配置音色/角色/旁白文本”
                    W->>F: 推送前置条件错误
                    F->>F: 引导完善配置
                else 前置条件完备
                    Note over A: 📊 标准化权限处理流程
                end
            end
        end
        A->>DB: 检查分镜访问权限(事务锁定)

        alt 权限不足
            Note over A: 用户无权访问该分镜
            A->>W: 返回权限不足详细信息<br/>{<br/>  code: 403,<br/>  message: "无权访问分镜",<br/>  data: null,<br/>  timestamp, request_id<br/>}
            W->>F: 推送权限不足消息
            F->>F: 显示权限不足提示
            Note over A: 无操作权限，保护数据安全
        else 权限充足
            A->>DB: 获取分镜旁白配置
            A->>R: 同步配置状态(缓存更新)
            A->>DB: 写入业务日志(状态:开始)
            A->>R: 缓存业务日志
            A->>RM: 创建音频元数据记录（仅URL/状态/元信息）

            Note over A: 📈 实时进度推送
            A->>W: 推送进度更新(10%, "开始音色试听")
            W->>F: 实时推送进度到前端
            F->>F: 更新进度条显示

            Note over A: 🎯 执行音色试听处理
            alt 快速试听模式
                A->>W: 推送进度更新(30%, "使用当前配置生成试听")
                W->>F: 实时推送进度
                A->>A: 调用StoryboardNarrationService快速试听
                A->>A: 使用现有配置生成试听
                A->>W: 推送进度更新(60%, "生成试听音频")
                W->>F: 实时推送进度
            else 完整配置模式
                A->>W: 推送进度更新(30%, "连接AI平台")
                W->>F: 实时推送进度
                A->>SC: StoryboardNarrationService调用AI语音合成服务
                A->>W: 推送进度更新(60%, "AI语音合成中")
                W->>F: 实时推送进度
                SC->>AI: 生成试听音频
                AI->>SC: 返回音频结果
                SC->>A: 返回音频URL与元数据给StoryboardNarrationService
            end

            A->>W: 推送进度更新(80%, "保存试听数据")
            W->>F: 实时推送进度

            alt 音色试听失败
                A->>W: 返回失败结果<br/>{<br/>  code: 5002,<br/>  message: "音色试听失败",<br/>  data: error_details,<br/>  timestamp, request_id<br/>}
                W->>F: 推送失败结果
                A->>DB: 更新业务日志(状态:失败)
                A->>R: 同步失败状态
                A->>RM: 更新元数据状态=failed（仅URL/状态，不做文件操作）
                A->>E: POST /py-api/events/publish<br/>发布失败事件(异步处理)<br/>{<br/>  "event_type": "voice_audition_failed",<br/>  "business_id": "audition_task_id",<br/>  "user_id": user_id,<br/>  "error_details": {<br/>    "voice_provider": "volcengine",<br/>    "error_code": "AI_SERVICE_ERROR",<br/>    "error_message": "音色试听生成失败"<br/>  },<br/>  "metadata": {<br/>    "storyboard_id": 123,<br/>    "voice_config": {...},<br/>    "preview_text": "试听文本"<br/>  }<br/>}
                F->>F: 显示试听失败提示
                Note over A: 仅更新元数据状态，遵循资源下载架构边界
            else 音色试听成功
                A->>DB: 更新业务日志(状态:成功)
                A->>DB: 写入last_preview_url(便于复用)
                A->>R: 同步最终状态
                A->>RM: 更新元数据（status=ready, url，仅URL/状态/元信息）

                A->>W: 推送进度更新(100%, "音色试听完成")
                W->>F: 实时推送最终完成状态
                F->>F: 显示试听成功界面

                A->>W: 返回试听音频数据
                W->>F: 推送音色试听成功结果
                Note over F: 前端使用音频URL直接播放（服务器不做中转）
                F->>F: 播放试听音频

                Note over A: 📊 用户偏好学习与优化
                A->>DB: 记录用户音色选择行为和偏好权重
                A->>R: 更新用户常用音色缓存
                A->>R: 刷新推荐算法缓存
            end
        end

        Note over F: 🔚 清理资源
        F->>W: 关闭WebSocket连接
    end

    Note over F: 🎯 音色试听完成，继续后续业务流程

    Note over F,AI: 🚨 架构边界规范遵循
        </div>

        <div class="features">
            <h3>🎯 可复用流程特性</h3>
            <ul>
                <li><strong>🔄 标准化调用接口：</strong>统一的参数化调用方式，支持不同业务场景的音色试听需求</li>
                <li><strong>🔐 标准化Token验证：</strong>复用diagram-22-python-token-validation.html标准Token验证流程</li>
                <li><strong>🎨 UI统一处理：</strong>所有音色试听的UI交互都由"Py视频创作工具前端"统一处理，确保用户体验一致性</li>
                <li><strong>📋 多模式支持：</strong>支持快速试听、完整配置两种调用模式</li>
                <li><strong>🎭 双配置模式：</strong>支持解说角色选择和音色参数配置两种设置方式</li>
                <li><strong>🤖 智能音色选择：</strong>集成标准化的AI音色推荐机制，提供最佳推荐</li>
                <li><strong>🔒 完整权限处理：</strong>包含分镜访问权限验证、用户状态检查的完整流程</li>
                <li><strong>📡 实时进度推送：</strong>通过WebSocket提供实时的试听生成进度反馈</li>
                <li><strong>⚠️ 错误处理机制：</strong>完善的错误处理和用户提示机制</li>
                <li><strong>🔄 状态管理：</strong>完整的业务状态跟踪和数据同步</li>
                <li><strong>📊 用户偏好学习：</strong>记录用户音色选择行为，优化后续推荐</li>
                <li><strong>🎯 结果回调：</strong>标准化的成功/失败结果返回机制</li>
                <li><strong>🚨 架构边界遵循：</strong>严格遵循资源下载架构边界，仅返回URL与元数据</li>
                <li><strong>🗂️ 资源管理边界（RM）：</strong>仅管理元数据（URL/状态/元信息），不保存实际资源文件</li>
                <li><strong>🔄 复用机制：</strong>last_preview_url写入便于前端复用，避免重复生成相同配置的试听音频</li>
            </ul>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });

        // 图表已直接在HTML中定义，无需JavaScript渲染
    </script>
</body>
</html>
