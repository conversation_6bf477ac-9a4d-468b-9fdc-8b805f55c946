<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享业务流程: AI生成提示词（可复用标准流程）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid { text-align: center; }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .features {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding-left: 0;
        }
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .reference-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .reference-box h4 {
            color: #856404;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='all-diagrams-index.html'">← 返回图表索引</button>

    <div class="container">
        <h1>✍️ 共享业务流程: AI生成提示词（可复用标准流程）</h1>

        <div class="description">
            <strong>流程说明：</strong>本流程定义"智能扩写/生成提示词"的标准化业务组件，供分镜编辑、文案编辑、角色/场景描述等功能复用。严格复用通用机制：Token验证、智能平台选择、标准化积分处理、WebSocket进度推送与事件总线。UI交互参考右侧选择平台、左侧弹出"智能扩写"对话框，点击"使用扩写写结果"回填到输入框的交互范式。
        </div>

        <div class="reference-box">
            <h4>📋 引用说明</h4>
            <p><strong>可被以下流程引用：</strong>分镜编辑（回填至 storyboard.ai_prompt 或 scene_description）、故事创作、角色描述、旁白脚本等。</p>
            <p><strong>标准引用方式：</strong>PromptEditor.start({ context, source_field, target_field, project_id, storyboard_id, autosave })</p>
        </div>

        <div class="mermaid">
sequenceDiagram
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant AI as AI平台
    participant E as 事件总线

    Note over F: 🔄 用户发起"智能扩写/生成提示词"
    F->>F: 启动 PromptEditor({context,project_id,storyboard_id})

    Note over F: 📋 参数化配置处理
    alt context=storyboard（分镜编辑）
        Note over F: 显示分镜提示词扩写界面，目标字段ai_prompt
    else context=scene_description（场景描述）
        Note over F: 显示场景描述扩写界面，目标字段scene_description
    else context=character（角色描述）
        Note over F: 显示角色描述扩写界面
    else context=narration（旁白脚本）
        Note over F: 显示旁白脚本扩写界面
    end

    Note over F: 🎨 统一UI处理
    F->>F: 弹出智能扩写对话框界面
    F->>A: 获取扩写初始配置数据

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限
    alt Token验证失败
        A->>F: 返回认证失败<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
        F->>F: 显示登录提示
    else Token验证通过
        A->>DB: 载入用户/项目状态
        A->>F: 返回Prompt可选模板/限制
    end

    Note over F: 🤖 智能平台选择流程（引用 C-1）
    F->>A: POST /py-api/ai-models/select-platform<br/>business_type=text, auto_recommend=true
    A->>A: 调用AiPlatformSelectionService
    A->>DB: 查询AI模型配置和可用平台
    A->>DB: 查询用户历史偏好和使用记录
    A->>A: 分析提示词内容特征+用户偏好+平台状态
    A->>A: 返回最佳推荐+备选方案
    A->>F: 返回平台推荐结果
    F->>F: 显示平台选择界面（可选）

    Note over F: 🪟 用户点击"智能扩写"按钮，弹出对话框输入原文
    F->>W: POST /py-api/websocket/auth<br/>建立WebSocket认证连接<br/>{<br/>  "client_type": "python_tool",<br/>  "user_token": "Bearer xxx",<br/>  "business_type": "text_generation"<br/>}
    W->>W: 验证用户Token和客户端类型
    W->>DB: 创建WebSocket会话记录
    W->>F: 返回WebSocket连接信息<br/>{<br/>  code: 200,<br/>  message: "WebSocket认证成功",<br/>  data: {<br/>    session_id: "ws_session_xxx",<br/>    websocket_url: "wss://api.tiptop.cn:8080",<br/>    expires_at: "2025-01-09T10:30:00Z",<br/>    supported_events: [<br/>      "ai_generation_progress",<br/>      "ai_generation_completed",<br/>      "ai_generation_failed"<br/>    ]<br/>  }<br/>}
    F->>W: 连接到WebSocket服务器<br/>wss://api.tiptop.cn:8080?session_id=ws_session_xxx
    W->>F: WebSocket连接确认，准备接收进度推送

    Note over A: 📊 标准化积分处理流程
    F->>A: POST /py-api/ai/text/generate-with-websocket<br/>文本扩写请求<br/>{<br/>  "prompt": "请扩写这段画面描述：夕阳下的古城",<br/>  "model_id": 12,<br/>  "project_id": 123,<br/>  "max_tokens": 400,<br/>  "temperature": 0.7,<br/>  "context": "prompt_edit",<br/>  "websocket_session_id": "ws_session_xxx"<br/>}
    A->>A: 创建异步任务ProcessTextGeneration
    A->>F: 立即返回任务信息<br/>{<br/>  "task_id": "text_gen_123456",<br/>  "status": "processing",<br/>  "context": "prompt_edit"<br/>}

    Note over A: 🔄 异步任务开始处理
    A->>DB: 检查用户积分(事务锁定)

    alt 积分不足
        Note over A: 积分 < 所需积分
        A->>W: 推送积分不足消息<br/>{<br/>  "event": "ai_generation_failed",<br/>  "task_id": "text_gen_123456",<br/>  "error_message": "积分不足"<br/>}
        W->>F: 推送积分不足消息
        F->>F: 显示积分不足提示
        Note over A: 无扣费操作，保护用户资金
    else 积分充足
        A->>DB: 扣取积分(冻结状态)
        A->>R: 同步积分状态(缓存更新)
        A->>DB: 写入业务日志(状态:冻结)
        A->>R: 缓存业务日志
        Note over A: 📈 实时进度推送
        A->>W: 推送进度更新(10%, "开始扩写")
        W->>F: 实时推送进度到前端
        F->>F: 更新进度条显示

        Note over A: 🎯 执行扩写处理
        A->>W: 推送进度更新(30%, "连接AI平台")
        W->>F: 实时推送进度
        A->>A: 调用内部文本生成服务（AiGenerationService）
        A->>W: 推送进度更新(60%, "AI处理中")
        W->>F: 实时推送进度
        A->>SC: AiGenerationService调用AiServiceClient
        SC->>AI: 发送生成请求
        AI->>SC: 返回扩写文本
        SC->>A: 返回AI响应给AiGenerationService
        A->>A: 处理text与元数据

        A->>W: 推送进度更新(90%, "保存元数据")
        W->>F: 实时推送进度

        alt 扩写失败
            A->>DB: 业务日志(失败) + 退还积分
            A->>R: 同步退还
            A->>E: POST /py-api/events/publish<br/>发布失败事件(异步处理)<br/>{<br/>  "event_type": "text_generation_failed",<br/>  "business_id": "text_task_id",<br/>  "user_id": user_id,<br/>  "error_details": {<br/>    "context": "prompt_edit",<br/>    "error_code": "AI_SERVICE_ERROR",<br/>    "error_message": "文本生成失败"<br/>  },<br/>  "metadata": {<br/>    "prompt": "...",<br/>    "generation_params": {...},<br/>    "platform": "deepseek"<br/>  }<br/>}
            A->>W: 返回失败结果<br/>{<br/>  code: 5002,<br/>  message: "扩写失败",<br/>  data: error_details,<br/>  timestamp, request_id<br/>}
            W->>F: 推送失败结果
            F->>F: 显示扩写失败提示
        else 扩写成功
            A->>DB: 业务日志(成功) + 确认积分扣取
            A->>R: 同步最终状态
            A->>W: 推送进度更新(100%, "扩写完成")
            W->>F: 推送扩写成功结果
            F->>F: 在弹窗中展示扩写结果
            Note over F: 用户点击"使用扩写写结果"
            F->>F: 将 text 回填到目标输入框

            opt 自动保存（可选）
                F->>A: PUT /py-api/storyboards/{id}<br/>body: { ai_prompt: text }
                A->>DB: 更新分镜.ai_prompt（纯文本）
                A->>F: 返回更新成功<br/>{<br/>  code: 200,<br/>  message: "更新成功",<br/>  data: {storyboard_id},<br/>  timestamp, request_id<br/>}
            end

            F->>W: 关闭WebSocket连接
            F->>F: 显示扩写完成结果
        end
    end

    Note over F: 🎯 提示词扩写完成，继续后续业务（如生成画面/保存草稿）
        </div>

        <div class="features">
            <h3>🎯 可复用流程特性</h3>
            <ul>
                <li>🔐 标准化Token验证（引用 diagram-22-python-token-validation.html）</li>
                <li>🤖 智能平台选择（引用 C-1，AiServiceClient）</li>
                <li>💰 标准化积分处理（C-2/C-3/C-4）与事务保障</li>
                <li>📡 WebSocket 实时进度推送，统一连接管理</li>
                <li>🧩 组件化回填：对话框"使用扩写写结果"→ 目标输入框</li>
                <li>📝 纯文本与元数据保存，严格遵守资源下载架构边界（不生成/中转文件）</li>
                <li>🧪 统一响应格式：JSON 垂直格式化返回</li>
            </ul>
            <h3>📋 调用参数规范</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔧 调用接口</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 调用提示词扩写流程（2025-08-08 标准版）
PromptEditor.start({
    context: 'storyboard|scene_description|character|narration',
    source_field: 'scene_description',  // 源字段名
    target_field: 'ai_prompt',          // 目标字段名
    project_id: 123,
    storyboard_id: 456,                 // 可选，分镜编辑时提供
    autosave: true,                     // 是否自动保存
    config: {
        showPlatformSelection: true,
        enableQuickPrompts: true,       // 显示快速提示词模板
        maxTokens: 4000,               // 最大扩写长度
        showPreview: true              // 显示预览功能
    },
    callbacks: {
        onSuccess: (result) => { /* 成功回调，包含扩写文本 */ },
        onCancel: () => { /* 取消回调 */ },
        onError: (error) => { /* 错误回调 */ },
        onProgress: (progress) => { /* 进度回调 */ }
    }
});

// API接口调用示例
// 1. AI文本生成（扩写版 - WebSocket异步处理）
POST /py-api/ai/text/generate-with-websocket
{
    "prompt": "请扩写这段画面描述：夕阳下的古城",
    "model_id": 12,
    "project_id": 123,
    "max_tokens": 400,
    "temperature": 0.7,
    "context": "prompt_edit",
    "websocket_session_id": "ws_session_xxx"
}

// 2. 分镜更新（支持ai_prompt）
PUT /py-api/storyboards/{id}
{
    "ai_prompt": "夕阳西下，古老的城墙在金色光辉中显得格外庄严..."
}
                </pre>

                <h4>📤 返回结果格式</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 成功结果
{
    code: 200,
    message: "扩写完成",
    data: {
        text: "扩写后的完整文本内容...",
        platform: "deepseek",
        cost: "0.0050",
        tokens_used: 245,
        original_length: 12,
        expanded_length: 156
    },
    timestamp: 1640995200,
    request_id: "req_prompt_abc123"
}

// 失败结果（遵循API规范格式）
{
    code: 1006,                    // 业务错误码：1006=积分不足, 401=认证失败, 422=参数验证失败, 5002=扩写失败
    message: "积分不足",           // 业务错误码描述
    data: {                       // 错误详细数据（可选）
        error_details: 'insufficient_credits',
        user_cancelled: false,
        expansion_step: 'generation|processing|saving'
    },
    timestamp: 1640995200,        // 时间戳
    request_id: "req_prompt_def456" // 请求ID
}
                </pre>
            </div>

            <h3>🔗 引用示例</h3>
            <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔗 在分镜编辑流程中引用</h4>
                <p>当分镜编辑需要智能扩写提示词时，调用此流程：</p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
// 在分镜编辑界面中
Note over F: 用户点击"智能扩写"按钮
F->>PromptEditor: 调用提示词扩写流程(storyboard模式)
Note over PromptEditor: 引用 diagram-prompt-edit-shared.html
PromptEditor->>F: 返回扩写的提示词文本
F->>StoryboardEdit: 自动回填到ai_prompt字段
                </pre>

                <h4>🔗 在角色描述中引用</h4>
                <p>当角色创建需要扩写描述时，调用此流程：</p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
// 在角色创建界面中
Note over F: 用户点击角色描述"智能扩写"
F->>PromptEditor: 调用提示词扩写流程(character模式)
Note over PromptEditor: 引用 diagram-prompt-edit-shared.html
PromptEditor->>F: 返回扩写的角色描述
F->>CharacterCreation: 自动回填到描述字段
                </pre>
            </div>

            <h3>📚 技术规范说明</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <p><strong>本流程遵循以下技术规范：</strong></p>
                <ul>
                    <li><strong>C-1：</strong>AI任务调度 - 智能平台选择机制</li>
                    <li><strong>C-2：</strong>AI生成成功 - 积分确认扣取流程</li>
                    <li><strong>C-3：</strong>积分不足 - 快速验证机制</li>
                    <li><strong>C-4：</strong>AI生成失败 - 事件总线异步处理</li>
                    <li><strong>C-9：</strong>环境切换 - AiServiceClient统一调用</li>
                    <li><strong>资源下载架构边界：</strong>仅处理文本与元数据，不生成/中转资源文件</li>
                    <li><strong>WebSocket使用边界：</strong>仅Py视频创作工具使用，统一连接管理</li>
                </ul>
                <p><em>这些规范确保了与系统其他流程的一致性和兼容性。</em></p>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35
            }
        });
    </script>
</body>
</html>
