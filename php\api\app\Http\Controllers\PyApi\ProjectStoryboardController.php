<?php

namespace App\Http\Controllers\PyApi;

use App\Http\Controllers\Controller;
use App\Services\PyApi\ProjectStoryboardService;
use App\Services\PyApi\WebSocketService;
use App\Services\PyApi\WebSocketEventService;
use App\Services\AiServiceClient;
use App\Jobs\ProcessStoryboardImageGeneration;
use App\Services\AuthService;
use App\Enums\ApiCodeEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Helpers\LogCheckHelper;

/**
 * 项目分镜管理控制器
 * 提供分镜的CRUD操作接口
 */
class ProjectStoryboardController extends Controller
{
    protected $storyboardService;
    protected $webSocketService;
    protected $webSocketEventService;

    public function __construct(
        ProjectStoryboardService $storyboardService,
        WebSocketService $webSocketService,
        WebSocketEventService $webSocketEventService
    ) {
        $this->storyboardService = $storyboardService;
        $this->webSocketService = $webSocketService;
        $this->webSocketEventService = $webSocketEventService;
    }

    /**
     * 获取项目分镜列表
     * @ApiMethod (GET)
     * @ApiRoute (/py-api/projects/{project_id}/storyboards)
     * @ApiParams (name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams (name="status", type="string", required=false, description="分镜状态筛选")
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="per_page", type="int", required=false, description="每页数量，默认10")
     */
    public function index($projectId, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            
            // 验证项目权限
            $projectCheck = $this->storyboardService->checkProjectAccess($user->id, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $this->errorResponse($projectCheck['code'], $projectCheck['message']);
            }

            $filters = [
                'status' => $request->get('status'),
            ];
            
            $page = (int) $request->get('page', 1);
            $perPage = (int) $request->get('per_page', 10);

            $result = $this->storyboardService->getProjectStoryboards($projectId, $filters, $page, $perPage);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('获取项目分镜列表失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取分镜列表失败', []);
        }
    }

    /**
     * 获取分镜详情
     * @ApiMethod (GET)
     * @ApiRoute (/py-api/storyboards/{id})
     * @ApiParams (name="id", type="int", required=true, description="分镜ID")
     */
    public function show($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->storyboardService->getStoryboardDetail($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('获取分镜详情失败', [
                'method' => __METHOD__,
                'storyboard_id' => $id,
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取分镜详情失败', []);
        }
    }

    /**
     * 创建分镜
     * @ApiMethod (POST)
     * @ApiRoute (/py-api/projects/{project_id}/storyboards)
     * @ApiParams (name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams (name="scene_title", type="string", required=true, description="分镜标题")
     * @ApiParams (name="scene_description", type="string", required=true, description="分镜描述")
     * @ApiParams (name="scene_number", type="int", required=false, description="分镜序号，不传则自动生成")
     * @ApiParams (name="camera_angle", type="string", required=false, description="镜头角度")
     * @ApiParams (name="shot_type", type="string", required=false, description="镜头类型")
     * @ApiParams (name="duration", type="int", required=false, description="预计时长（秒）")
     */
    public function store($projectId, Request $request)
    {
        try {
            $rules = [
                'scene_title' => 'required|string|max:200',
                'scene_description' => 'required|string|max:2000',
                'scene_number' => 'sometimes|integer|min:1',
                'camera_angle' => 'sometimes|string|max:50',
                'shot_type' => 'sometimes|string|max:50',
                'lighting' => 'sometimes|string|max:100',
                'background_description' => 'sometimes|string|max:1000',
                'duration' => 'sometimes|integer|min:1|max:300'
            ];

            $messages = [
                'scene_title.required' => '分镜标题不能为空',
                'scene_title.max' => '分镜标题不能超过200个字符',
                'scene_description.required' => '分镜描述不能为空',
                'scene_description.max' => '分镜描述不能超过2000个字符',
                'scene_number.min' => '分镜序号必须大于0',
                'duration.min' => '时长必须大于0秒',
                'duration.max' => '时长不能超过300秒'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $storyboardData = [
                'scene_title' => $request->scene_title,
                'scene_description' => $request->scene_description,
                'scene_number' => $request->scene_number,
                'camera_angle' => $request->camera_angle,
                'shot_type' => $request->shot_type,
                'lighting' => $request->lighting,
                'background_description' => $request->background_description,
                'duration' => $request->duration
            ];

            $result = $this->storyboardService->createStoryboard($user->id, $projectId, $storyboardData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('创建分镜失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '创建分镜失败', []);
        }
    }

    /**
     * 更新分镜
     * @ApiMethod (PUT)
     * @ApiRoute (/py-api/storyboards/{id})
     * @ApiParams (name="id", type="int", required=true, description="分镜ID")
     */
    public function update($id, Request $request)
    {
        try {
            $rules = [
                'scene_title' => 'sometimes|string|max:200',
                'scene_description' => 'sometimes|string|max:2000',
                'scene_number' => 'sometimes|integer|min:1',
                'camera_angle' => 'sometimes|string|max:50',
                'shot_type' => 'sometimes|string|max:50',
                'lighting' => 'sometimes|string|max:100',
                'background_description' => 'sometimes|string|max:1000',
                'duration' => 'sometimes|integer|min:1|max:300',
                'status' => 'sometimes|in:draft,approved,generating,completed,failed',
                'ai_prompt' => 'sometimes|string|max:4000'
            ];

            $this->validateData($request->all(), $rules, [], []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $updateData = $request->only([
                'scene_title', 'scene_description', 'scene_number', 'camera_angle',
                'shot_type', 'lighting', 'background_description', 'duration', 'status', 'ai_prompt'
            ]);

            $result = $this->storyboardService->updateStoryboard($id, $user->id, $updateData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('更新分镜失败', [
                'method' => __METHOD__,
                'storyboard_id' => $id,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '更新分镜失败', []);
        }
    }

    /**
     * 删除分镜
     * @ApiMethod (DELETE)
     * @ApiRoute (/py-api/storyboards/{id})
     * @ApiParams (name="id", type="int", required=true, description="分镜ID")
     */
    public function destroy($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->storyboardService->deleteStoryboard($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('删除分镜失败', [
                'method' => __METHOD__,
                'storyboard_id' => $id,
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '删除分镜失败', []);
        }
    }

    /**
     * 批量更新分镜排序
     * @ApiMethod (PUT)
     * @ApiRoute (/py-api/projects/{project_id}/storyboards/reorder)
     * @ApiParams (name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams (name="storyboards", type="array", required=true, description="分镜排序数据")
     */
    public function reorder($projectId, Request $request)
    {
        try {
            $rules = [
                'storyboards' => 'required|array|min:1',
                'storyboards.*.id' => 'required|integer',
                'storyboards.*.scene_number' => 'required|integer|min:1'
            ];

            $messages = [
                'storyboards.required' => '分镜排序数据不能为空',
                'storyboards.array' => '分镜排序数据格式错误',
                'storyboards.*.id.required' => '分镜ID不能为空',
                'storyboards.*.scene_number.required' => '分镜序号不能为空'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->storyboardService->reorderStoryboards($user->id, $projectId, $request->storyboards);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('分镜排序失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '分镜排序失败', []);
        }
    }

    /**
     * 批量生成分镜（WebSocket版本） 
     * @ApiMethod (POST)
     * @ApiRoute (/py-api/projects/{project_id}/storyboards/batch-generate-with-websocket)
     * @ApiParams (name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams (name="storyboard_ids", type="array", required=false, description="指定分镜ID列表，不传则生成所有未生成的分镜")
     * @ApiParams (name="mode", type="string", required=false, description="生成模式：batch、selective、project")
     * @ApiParams (name="filters", type="object", required=false, description="筛选条件（selective模式使用）")
     * @ApiParams (name="generation_params", type="object", required=false, description="生成参数")
     * @ApiParams (name="websocket_session_id", type="string", required=false, description="WebSocket会话ID（用于进度推送）")
     */
    public function batchGenerateWithWebSocket($projectId, Request $request)
    {
        try {
            $rules = [
                'storyboard_ids' => 'sometimes|array',
                'storyboard_ids.*' => 'integer',
                'mode' => 'sometimes|string|in:batch,selective,project',
                'filters' => 'sometimes|array',
                'filters.status' => 'sometimes|array',
                'filters.has_ai_prompt' => 'sometimes|boolean',
                'filters.scene_types' => 'sometimes|array',
                'generation_params' => 'sometimes|array',
                'websocket_session_id' => 'sometimes|string'
            ];

            $this->validateData($request->all(), $rules, [], []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $mode = $request->get('mode', 'batch');
            $storyboardIds = $request->get('storyboard_ids', []);
            $filters = $request->get('filters', []);
            $generationParams = $request->get('generation_params', []);
            $webSocketSessionId = $request->get('websocket_session_id');

            // 🤖 智能平台选择流程
            if (empty($generationParams['platform'])) {
                $platformResult = AiServiceClient::getUserRecommendations(
                    $user->id,
                    'storyboard_image_generation',
                    1
                );

                if ($platformResult['success'] && !empty($platformResult['data']['recommendations'])) {
                    $generationParams['platform'] = $platformResult['data']['recommendations'][0]['platform_key'];
                } else {
                    $generationParams['platform'] = 'liblib'; // 默认平台
                }
            }

            // 获取需要生成的分镜ID列表
            $targetStoryboardIds = $this->getTargetStoryboardIds($user->id, $projectId, $mode, $storyboardIds, $filters);

            if (empty($targetStoryboardIds)) {
                return $this->errorResponse(ApiCodeEnum::NOT_FOUND, '没有找到需要生成的分镜', []);
            }

            // 生成任务ID
            $taskId = 'storyboard_' . time() . '_' . Str::random(8);

            // 如果有WebSocket会话ID，验证会话
            if ($webSocketSessionId) {
                $sessionValid = $this->webSocketService->validateSession($webSocketSessionId, $user->id);
                if (!$sessionValid) {
                    return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, 'WebSocket会话无效', []);
                }
            }

            // 创建异步任务
            ProcessStoryboardImageGeneration::dispatch(
                $taskId,
                $user->id,
                $targetStoryboardIds,
                $generationParams,
                $mode
            );

            // 立即返回任务信息
            $responseData = [
                'task_id' => $taskId,
                'status' => 'processing',
                'mode' => $mode,
                'total_storyboards' => count($targetStoryboardIds),
                'storyboard_ids' => $targetStoryboardIds,
                'generation_params' => $generationParams,
                'websocket_session_id' => $webSocketSessionId,
                'estimated_time' => count($targetStoryboardIds) * 30, // 预估每个分镜30秒
                'timestamp' => now()->toISOString(),
                'request_id' => 'req_' . Str::random(16)
            ];

            Log::info('分镜图片生成任务创建成功', [
                'task_id' => $taskId,
                'user_id' => $user->id,
                'project_id' => $projectId,
                'mode' => $mode,
                'total_storyboards' => count($targetStoryboardIds),
                'platform' => $generationParams['platform']
            ]);

            return $this->successResponse($responseData, '分镜图片生成任务创建成功');

        } catch (\Exception $e) {
            Log::error('批量生成分镜失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批量生成分镜失败', []);
        }
    }

    /**
     * 获取目标分镜ID列表
     */
    private function getTargetStoryboardIds(int $userId, int $projectId, string $mode, array $storyboardIds, array $filters): array
    {
        switch ($mode) {
            case 'selective':
                return $this->storyboardService->getSelectiveStoryboardIds($userId, $projectId, $filters);

            case 'project':
                return $this->storyboardService->getAllProjectStoryboardIds($userId, $projectId);

            case 'batch':
            default:
                if (!empty($storyboardIds)) {
                    return $this->storyboardService->validateStoryboardIds($userId, $projectId, $storyboardIds);
                } else {
                    return $this->storyboardService->getUnprocessedStoryboardIds($userId, $projectId);
                }
        }
    }

    /**
     * 获取分镜生成任务状态
     * @ApiMethod (GET)
     * @ApiRoute (/py-api/storyboards/tasks/{task_id}/status)
     * @ApiParams (name="task_id", type="string", required=true, description="任务ID")
     */
    public function getTaskStatus($taskId, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 查询任务状态
            $result = $this->storyboardService->getGenerationTaskStatus($taskId, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('获取任务状态失败', [
                'method' => __METHOD__,
                'task_id' => $taskId,
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取任务状态失败', []);
        }
    }
}
